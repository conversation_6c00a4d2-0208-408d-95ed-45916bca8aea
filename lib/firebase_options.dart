// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAd6rHynqmQt19dMTJPuD2HfBWgfuJXTuU',
    appId: '1:689517663804:web:b565d5e6d454da28805539',
    messagingSenderId: '689517663804',
    projectId: 'abaya-app-1cb97',
    authDomain: 'abaya-app-1cb97.firebaseapp.com',
    storageBucket: 'abaya-app-1cb97.firebasestorage.app',
    measurementId: 'G-QEYNJZJ3MD',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBP5wH45L5rvPvzCzB-V0jAuGDQOA5ZTDI',
    appId: '1:689517663804:android:bd9ac358e983f4b3805539',
    messagingSenderId: '689517663804',
    projectId: 'abaya-app-1cb97',
    storageBucket: 'abaya-app-1cb97.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDolUQNdjsMNc7J30Yy0Rf9XEnVYV67Av4',
    appId: '1:689517663804:ios:24aef8e00ac8805a805539',
    messagingSenderId: '689517663804',
    projectId: 'abaya-app-1cb97',
    storageBucket: 'abaya-app-1cb97.firebasestorage.app',
    iosBundleId: 'com.beshr.beshrTok',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDolUQNdjsMNc7J30Yy0Rf9XEnVYV67Av4',
    appId: '1:689517663804:ios:24aef8e00ac8805a805539',
    messagingSenderId: '689517663804',
    projectId: 'abaya-app-1cb97',
    storageBucket: 'abaya-app-1cb97.firebasestorage.app',
    iosBundleId: 'com.beshr.beshrTok',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAd6rHynqmQt19dMTJPuD2HfBWgfuJXTuU',
    appId: '1:689517663804:web:ebc1fb13bb0a1fbb805539',
    messagingSenderId: '689517663804',
    projectId: 'abaya-app-1cb97',
    authDomain: 'abaya-app-1cb97.firebaseapp.com',
    storageBucket: 'abaya-app-1cb97.firebasestorage.app',
    measurementId: 'G-9QHHZWJTSP',
  );
}
