import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:beshr_tok/screens/feed_screen.dart';
import 'package:beshr_tok/service_locator.dart';

import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase only if configuration is available
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    print('Firebase initialization failed: $e');
    print('Running without Firebase...');
  }

  setup();
  runApp(MaterialApp(
    debugShowCheckedModeBanner: false,
    home: FeedScreen(),
  ));
}
